@let screenSize = "md" | responsive | async;
@let screenIsMobile = screenSize === false;

<ng-template #grid let-data="data" let-layout="layout" let-title="title" let-button="button" let-isButtonVisible="isButtonVisible">
  @let numberOfTiles = (layout.top === "hero" ? 1 : layout.top || 0) + (layout.bottom || 0);
  <div>
    @if (screenIsMobile) {
      <app-card-carousel class="flex md:!hidden" [title]="title">
        @for (girl of data; let index = $index; track girl) {
          @if (index < numberOfTiles) {
            <ng-template
              cardCarouselSlide
              [isAAContent]="girl.fieldPlusAccess !== true"
              [favoriteType]="'girl-infos'"
              [favoriteId]="girl.id"
              [image]="girl.fieldPublicImages?.[0]?.url"
              [title]="girl.fieldCategory?.[0]?.entity?.name"
              [text]="girl.name || girl.entityLabel"
              [month]="girl.descriptorMonth"
              [year]="girl.descriptorYear"
              [link]="['/girl', girl.girl.targetId]"
              [new]="IsNewFromTimestamp(girl.fieldLatestGalleryRelease) ? true : undefined"
            >
            </ng-template>
          }
        }
      </app-card-carousel>
    } @else {
      @let gridCols = (layout.top === 3 && layout.bottom === 4) || (layout.top === 4 && layout.top === 3)
        ? 12
        : (layout.bottom === 4 || layout.top === 4) ? 4 : (layout.bottom === 3 || layout.top === 3)
          ? 3
          : (layout.bottom === 2 || layout.top === 2) ? 2 : 1;
      @if (title) {
        <h2 class="mb-6 md:mb-10 py-4 w-full text-center">
          {{ title }}
        </h2>
      }
      <div
        class="grid gap-6"
        [class]="'grid-cols-' + gridCols"
      >
        @for (girl of data; let index = $index; track index) {
          @if (index < numberOfTiles) {
            <a
              [routerLink]="['/girl', girl.girl.targetId, girl.id]"
              class="flex h-full w-full"
              [class.w-full]="index === 0 && layout.top === 'hero'"
              [class.col-span-full]="index === 0 && layout.top === 'hero'"
              [class.col-span-3]="gridCols === 12 && layout.bottom === 4 && index >= 3"
              [class.col-span-4]="gridCols === 12 && layout.top === 3 && index < 3"
            >
              <app-gallery-card
                [isAAContent]="girl.fieldPlusAccess !== true"
                [favoriteType]="'girl-infos'"
                [favoriteId]="girl.id"
                [paywallImages]="girl.paywallImages"
                [image]="girl.fieldPublicImages?.[0]?.url"
                [title]="girl.fieldCategory?.[0]?.entity?.name"
                [month]="girl.descriptorMonth"
                [year]="girl.descriptorYear"
                [focalPoint]="{
                                x: girl.fieldMainFocalPointX || 50,
                                y: girl.fieldMainFocalPointY || 50,
                              }"
                [name]="girl.name || girl.entityLabel"
                [imageRatio]="index === 0 && layout.top === 'hero' ? 2 : 1"
                [overwriteImageRatio]="index === 0 && layout.top === 'hero' ? '2 / 1' : null"
                class="w-full"
                [autoSize]="false"
                [isNew]="IsNewFromTimestamp(girl.fieldLatestGalleryRelease) ? true : undefined"
              >
              </app-gallery-card>
            </a>
          }
        }
      </div>
    }
    @if (isButtonVisible && button?.url && button?.text) {
      <div class="flex px-3 md:px-0 mt-4 md:mt-10 justify-center">
        @if (isExternalUrl(button.url)) {
          <a
            [href]="button.url"
            target="_blank"
            class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
          >
            {{ button.text }}
          </a>
        } @else {
          <a
            [routerLink]="button.url"
            class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
          >
            {{ button.text }}
          </a>
        }
      </div>
    }
  </div>
</ng-template>

<div class="flex flex-col gap-16 py-6 lg:gap-20 lg:py-20">
  @if ($modules | async; as modules) {
    @for (module of modules; track module) {
      @if (module.entity.entityBundle === 'category_module' && module.categories) {
        <div>
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div class="grid gap-3 lg:gap-6 grid-cols-1 lg:grid-cols-4">
            @for (cat of module.categories; track cat.id) {
              <a
                [routerLink]="cat.link"
                class="flex h-full w-full"
                style="aspect-ratio: 1.5 / 1"
              >
                <app-category-tile
                  [image]="cat.image"
                  [tileTitle]="cat.tileTitle"
                  [focalPoint]="cat.focalPoint"
                ></app-category-tile>
              </a>
            }
          </div>
        </div>
      }
      @if (module.entity.entityBundle === 'e_paper_archiv_module' && module.magazines) {
        <div>
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div
            class="flex flex-wrap gap-6 md:gap-8 justify-center"
          >
            @for (mag of module.magazines; track mag.url) {
              <a [href]="mag.url" target="_blank" class="e-paper-card-container" [attr.aria-label]="mag.title">
                <app-article-preview
                  class="rounded-lg overflow-hidden"
                  style="aspect-ratio: 1 / 1.27"
                  [image]="mag.field_media_image || mag.field_archiv_image"
                ></app-article-preview>
              </a>
            }
          </div>
          @if (module.isButtonVisible && module.button.url && module.button.text) {
            <div class="flex px-3 md:px-0 mt-6 md:mt-10 justify-center">
              <a
                class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
                [href]="module.button.url"
                target="_blank"
              >
                {{ module.button.text }}
              </a>
            </div>
          }
        </div>
      }
      @if (module.entity.entityBundle === 'favorites_module') {
        <favorites-module
          [title]="module.title"
          [isGirlsVisible]="module.isGirlsVisible"
          [isGirlInfosVisible]="module.isGirlInfosVisible"
          [isVideosVisible]="module.isVideosVisible"
          [pageSize]="module.pageSize"
        />
      }
      @if (module.entity.entityBundle === 'feature_module') {
        <ng-container
          *ngTemplateOutlet="
            grid;
            context: {
              title: module.title,
              isButtonVisible: module.isButtonVisible,
              button: {
                url: module.button.url,
                text: module.button.text,
              },
              data: module.girls,
              layout: module.gridLayout,
            }
          "
        >
        </ng-container>
      }
      @if (module.entity.entityBundle === 'feed_module') {
        <feed-module [title]="module.title"/>
      }
      @if (module.entity.entityBundle === 'girl_des_tages_module' && module.girls) {
        <ng-container
          *ngTemplateOutlet="
          grid;
          context: {
            title: module.title,
            isButtonVisible: module.isButtonVisible,
            button: {
                url: module.button.url,
                text: module.button.text,
              },
            data: module.girls,
            layout: module.gridLayout,
          }
      "
        >
        </ng-container>
      }
      @if (module.entity.entityBundle === 'search_module') {
        <div class="py-20">
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div class="flex flex-col items-center relative mx-auto justify-center">
            <div class="w-full xl:w-1/2 relative z-30">
              <app-search-input
                (triggerSearch)="goToSearch($event)"
                [autofocus]="false"
                [externalMode]="true"
                placeholder="Suche"
              ></app-search-input>
            </div>
          </div>
        </div>
      }
      @if (module.entity.entityBundle === 'video_module') {
        @let maxNumberOfTiles = module.maxNumberOfTiles ?? 4;
        <div>
          @if (screenIsMobile) {
            <app-card-carousel
              class="flex md:!hidden"
              [title]="module.title"
            >
              @for (video of module.videos; track video; let index = $index) {
                @if (index < maxNumberOfTiles) {
                  <ng-template
                    cardCarouselSlide
                    [isAAContent]="getIsVideoAAContent(video)"
                    [favoriteType]="'video'"
                    [favoriteId]="video?.mid"
                    [link]="getVideoRouterLink(video)"
                    [title]="
                  video?.reverseFieldVideosMedia?.entities?.at(0)
                    ?.reverseGalleriesGirlInfo?.entities[0]?.fieldCategory?.[0]?.entity?.name
                "
                    [text]="video.entityLabel"
                    [image]="video.fieldPreviewImage.entity?.fieldMediaImage?.url"
                    [nexxID]="video.fieldNexxId"
                    [new]="IsNewFromTimestamp(video?.reverseFieldVideosMedia?.entities?.at(0)
                    ?.reverseGalleriesGirlInfo?.entities[0]?.fieldLatestGalleryRelease) ? true : undefined"
                  >
                  </ng-template>
                }
              }
            </app-card-carousel>
          } @else {
            @if (module.title) {
              <h2 class="mb-6 md:mb-10 py-4 w-full text-center">
                {{ module.title }}
              </h2>
            }
            <div class="grid gap-6 grid-cols-4 video-list">
              @for (video of module.videos; let index = $index; track index) {
                @if (index < maxNumberOfTiles) {
                  <a
                    [routerLink]="getVideoRouterLink(video)"
                    class="flex h-full w-full"
                  >
                    <app-gallery-card
                      [isAAContent]="getIsVideoAAContent(video)"
                      [favoriteType]="'video'"
                      [favoriteId]="video?.mid"
                      [title]="
                    video?.reverseFieldVideosMedia?.entities?.at(0)
                      ?.reverseGalleriesGirlInfo?.entities[0]?.fieldCategory?.[0]?.entity?.name
                  "
                      [image]="video.fieldPreviewImage.entity?.fieldMediaImage?.url"
                      [name]="video.entityLabel"
                      [nexxID]="video.fieldNexxId"
                      [isNew]="IsNewFromTimestamp(video?.reverseFieldVideosMedia?.entities?.at(0)
                    ?.reverseGalleriesGirlInfo?.entities[0]?.fieldLatestGalleryRelease) ? true : undefined"
                    >
                    </app-gallery-card>
                  </a>
                }
              }
            </div>
          }

          <div class="flex px-3 md:px-0 mt-6 md:mt-10 justify-center">
            <div
              routerLink="/videos"
              class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
            >
              Mehr Videos
            </div>
          </div>
        </div>
      }
    }
  }
</div>
